import { Link, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Calendar, 
  MapPin, 
  Clock, 
  Settings,
  ChevronLeft 
} from 'lucide-react';
import { clsx } from 'clsx';

const AdminLayout = ({ children }) => {
  const location = useLocation();

  const navigation = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: LayoutDashboard,
      current: location.pathname === '/admin'
    },
    {
      name: 'Bookings',
      href: '/admin/bookings',
      icon: Calendar,
      current: location.pathname === '/admin/bookings'
    },
    {
      name: 'Courts',
      href: '/admin/courts',
      icon: MapPin,
      current: location.pathname === '/admin/courts'
    },
    {
      name: 'Schedule',
      href: '/admin/schedule',
      icon: Clock,
      current: location.pathname === '/admin/schedule'
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: Settings,
      current: location.pathname === '/admin/settings'
    },
  ];

  return (
    <div className="min-h-screen bg-secondary-50">
      <div className="flex">
        {/* Sidebar - Hidden on mobile, shown on desktop */}
        <div className="hidden lg:block w-64 bg-white border-r border-secondary-200 min-h-screen">
          <div className="p-6">
            <Link to="/" className="flex items-center space-x-2 text-secondary-600 hover:text-primary-600 transition-colors">
              <ChevronLeft className="h-4 w-4" />
              <span className="text-sm">Back to Booking</span>
            </Link>
          </div>

          <nav className="px-4 space-y-2">
            {navigation.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={clsx(
                    'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors',
                    item.current
                      ? 'bg-primary-50 text-primary-700 border-r-2 border-primary-600'
                      : 'text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900'
                  )}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>

        {/* Main content */}
        <div className="flex-1 min-w-0">
          {/* Mobile header for admin */}
          <div className="lg:hidden bg-white border-b border-secondary-200 p-4">
            <div className="flex items-center justify-between">
              <h1 className="text-lg font-semibold text-secondary-900">Admin Panel</h1>
              <Link to="/" className="text-sm text-secondary-600 hover:text-primary-600">
                Back to Booking
              </Link>
            </div>
          </div>

          <main className="p-4 lg:p-8">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
