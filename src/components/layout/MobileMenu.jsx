import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Calendar, User, Settings, Clock, MapPin } from 'lucide-react';
import { Button } from '../ui';

const MobileMenu = ({ isAdmin = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const location = useLocation();

  // Close menu when location changes
  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  // Prevent scrolling when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const userNavigation = [
    { name: 'Book Courts', href: '/', icon: Calendar },
    { name: 'My Bookings', href: '/my-bookings', icon: User },
    { name: 'Admin Panel', href: '/admin', icon: Settings }
  ];

  const adminNavigation = [
    { name: 'Dashboard', href: '/admin', icon: Calendar },
    { name: 'Bookings', href: '/admin/bookings', icon: Calendar },
    { name: 'Courts', href: '/admin/courts', icon: MapPin },
    { name: 'Schedule', href: '/admin/schedule', icon: Clock },
    { name: 'Settings', href: '/admin/settings', icon: Settings },
    { name: 'Back to Booking', href: '/', icon: Calendar }
  ];

  const navigation = isAdmin ? adminNavigation : userNavigation;

  return (
    <>
      {/* Mobile menu button */}
      <div className="md:hidden">
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleMenu}
          aria-label={isOpen ? 'Close menu' : 'Open menu'}
        >
          {isOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </Button>
      </div>

      {/* Mobile menu overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={toggleMenu}
        />
      )}

      {/* Mobile menu panel */}
      <div
        className={`fixed top-0 right-0 bottom-0 w-64 bg-white z-50 transform transition-transform duration-300 ease-in-out md:hidden ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-secondary-200">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <Calendar className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-secondary-900">
                PadelBook
              </span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMenu}
              aria-label="Close menu"
              className="p-2 h-auto"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 overflow-y-auto p-4">
            <ul className="space-y-2">
              {navigation.map((item) => {
                const Icon = item.icon;
                const isActive = location.pathname === item.href;

                return (
                  <li key={item.name}>
                    <Link
                      to={item.href}
                      className={`flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                        isActive
                          ? 'bg-primary-50 text-primary-700'
                          : 'text-secondary-700 hover:bg-secondary-100 hover:text-secondary-900'
                      }`}
                    >
                      <Icon className="h-5 w-5 mr-3" />
                      {item.name}
                    </Link>
                  </li>
                );
              })}
            </ul>
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-secondary-200">
            <Button variant="outline" size="sm" className="w-full">
              <User className="h-4 w-4 mr-2" />
              {isAdmin ? 'Admin' : 'Login'}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileMenu;
