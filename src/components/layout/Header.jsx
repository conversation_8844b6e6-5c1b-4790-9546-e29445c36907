import { Link, useLocation } from 'react-router-dom';
import { Calendar, Settings, User } from 'lucide-react';
import Button from '../ui/Button';

const Header = () => {
  const location = useLocation();
  const isAdmin = location.pathname.startsWith('/admin');

  return (
    <header className="bg-white border-b border-secondary-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <Calendar className="h-5 w-5 text-white" />
            </div>
            <span className="text-xl font-bold text-secondary-900">
              PadelBook
            </span>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {!isAdmin ? (
              <>
                <Link 
                  to="/" 
                  className="text-secondary-700 hover:text-primary-600 transition-colors"
                >
                  Book Courts
                </Link>
                <Link 
                  to="/my-bookings" 
                  className="text-secondary-700 hover:text-primary-600 transition-colors"
                >
                  My Bookings
                </Link>
              </>
            ) : (
              <>
                <Link 
                  to="/admin" 
                  className="text-secondary-700 hover:text-primary-600 transition-colors"
                >
                  Dashboard
                </Link>
                <Link 
                  to="/admin/bookings" 
                  className="text-secondary-700 hover:text-primary-600 transition-colors"
                >
                  Bookings
                </Link>
                <Link 
                  to="/admin/courts" 
                  className="text-secondary-700 hover:text-primary-600 transition-colors"
                >
                  Courts
                </Link>
                <Link 
                  to="/admin/schedule" 
                  className="text-secondary-700 hover:text-primary-600 transition-colors"
                >
                  Schedule
                </Link>
              </>
            )}
          </nav>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            {!isAdmin ? (
              <>
                <Link to="/admin">
                  <Button variant="ghost" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Admin
                  </Button>
                </Link>
                <Button variant="outline" size="sm">
                  <User className="h-4 w-4 mr-2" />
                  Login
                </Button>
              </>
            ) : (
              <>
                <Link to="/">
                  <Button variant="ghost" size="sm">
                    Back to Booking
                  </Button>
                </Link>
                <Button variant="outline" size="sm">
                  <User className="h-4 w-4 mr-2" />
                  Admin
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
