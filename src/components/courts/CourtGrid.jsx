import { useState } from 'react';
import CourtCard from './CourtCard';
import { LoadingSpinner } from '../ui';

const CourtGrid = ({ 
  courts = [], 
  onCourtSelect, 
  selectedCourtId = null,
  loading = false,
  showAvailability = true 
}) => {
  const handleCourtSelect = (court) => {
    onCourtSelect?.(court);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (courts.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-secondary-400 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-secondary-900 mb-2">No courts available</h3>
        <p className="text-secondary-600">There are no courts matching your criteria.</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {courts.map((court) => (
        <CourtCard
          key={court.id}
          court={court}
          onSelect={handleCourtSelect}
          isSelected={selectedCourtId === court.id}
          showAvailability={showAvailability}
        />
      ))}
    </div>
  );
};

export default CourtGrid;
