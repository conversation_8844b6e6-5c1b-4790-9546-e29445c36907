import { useState } from 'react';
import { Filter, X } from 'lucide-react';
import { Button, Select, Input, Badge } from '../ui';

const CourtFilters = ({ 
  onFiltersChange,
  availableAmenities = [],
  priceRange = { min: 0, max: 100 }
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filters, setFilters] = useState({
    availability: 'all',
    priceMin: priceRange.min,
    priceMax: priceRange.max,
    amenities: [],
    rating: 'all'
  });

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.(newFilters);
  };

  const handleAmenityToggle = (amenity) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(a => a !== amenity)
      : [...filters.amenities, amenity];
    
    handleFilterChange('amenities', newAmenities);
  };

  const clearFilters = () => {
    const defaultFilters = {
      availability: 'all',
      priceMin: priceRange.min,
      priceMax: priceRange.max,
      amenities: [],
      rating: 'all'
    };
    setFilters(defaultFilters);
    onFiltersChange?.(defaultFilters);
  };

  const hasActiveFilters = filters.availability !== 'all' || 
                          filters.rating !== 'all' || 
                          filters.amenities.length > 0 ||
                          filters.priceMin !== priceRange.min ||
                          filters.priceMax !== priceRange.max;

  return (
    <div className="bg-white border border-secondary-200 rounded-lg p-4 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Filter className="h-5 w-5 text-secondary-600" />
          <h3 className="text-lg font-medium text-secondary-900">Filters</h3>
          {hasActiveFilters && (
            <Badge variant="primary" size="sm">
              {filters.amenities.length + 
               (filters.availability !== 'all' ? 1 : 0) + 
               (filters.rating !== 'all' ? 1 : 0) +
               (filters.priceMin !== priceRange.min || filters.priceMax !== priceRange.max ? 1 : 0)} active
            </Badge>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setIsOpen(!isOpen)}
          >
            {isOpen ? 'Hide' : 'Show'} Filters
          </Button>
        </div>
      </div>

      {isOpen && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Availability Filter */}
          <Select
            label="Availability"
            value={filters.availability}
            onChange={(e) => handleFilterChange('availability', e.target.value)}
          >
            <option value="all">All Courts</option>
            <option value="available">Available</option>
            <option value="busy">Busy</option>
          </Select>

          {/* Rating Filter */}
          <Select
            label="Minimum Rating"
            value={filters.rating}
            onChange={(e) => handleFilterChange('rating', e.target.value)}
          >
            <option value="all">Any Rating</option>
            <option value="4">4+ Stars</option>
            <option value="4.5">4.5+ Stars</option>
            <option value="5">5 Stars</option>
          </Select>

          {/* Price Range */}
          <div className="space-y-2">
            <label className="label text-secondary-700">Price Range ($/hr)</label>
            <div className="flex space-x-2">
              <Input
                type="number"
                placeholder="Min"
                value={filters.priceMin}
                onChange={(e) => handleFilterChange('priceMin', Number(e.target.value))}
                className="w-full"
              />
              <Input
                type="number"
                placeholder="Max"
                value={filters.priceMax}
                onChange={(e) => handleFilterChange('priceMax', Number(e.target.value))}
                className="w-full"
              />
            </div>
          </div>

          {/* Amenities */}
          <div className="space-y-2">
            <label className="label text-secondary-700">Amenities</label>
            <div className="flex flex-wrap gap-2">
              {availableAmenities.map((amenity) => (
                <Badge
                  key={amenity}
                  variant={filters.amenities.includes(amenity) ? 'primary' : 'default'}
                  className="cursor-pointer hover:opacity-80 transition-opacity"
                  onClick={() => handleAmenityToggle(amenity)}
                >
                  {amenity}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CourtFilters;
