import { MapPin, Users, Star, Clock } from 'lucide-react';
import { <PERSON>, Button, Badge } from '../ui';

const CourtCard = ({ 
  court, 
  onSelect, 
  isSelected = false,
  showAvailability = true 
}) => {
  const {
    id,
    name,
    description,
    image,
    capacity,
    rating,
    pricePerHour,
    amenities = [],
    availability = 'available'
  } = court;

  const availabilityConfig = {
    available: { color: 'success', text: 'Available' },
    busy: { color: 'warning', text: 'Busy' },
    unavailable: { color: 'error', text: 'Unavailable' }
  };

  return (
    <Card className={`transition-all duration-200 hover:shadow-medium cursor-pointer ${
      isSelected ? 'ring-2 ring-primary-500 shadow-medium' : ''
    }`}>
      <div onClick={() => onSelect?.(court)}>
        {/* Court Image */}
        <div className="relative h-48 bg-secondary-100 rounded-t-xl overflow-hidden">
          {image ? (
            <img 
              src={image} 
              alt={name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <MapPin className="h-12 w-12 text-secondary-400" />
            </div>
          )}
          
          {/* Availability Badge */}
          {showAvailability && (
            <div className="absolute top-3 right-3">
              <Badge variant={availabilityConfig[availability].color}>
                {availabilityConfig[availability].text}
              </Badge>
            </div>
          )}
          
          {/* Rating */}
          {rating && (
            <div className="absolute top-3 left-3 bg-white/90 backdrop-blur-sm rounded-lg px-2 py-1 flex items-center space-x-1">
              <Star className="h-4 w-4 text-warning-500 fill-current" />
              <span className="text-sm font-medium text-secondary-900">{rating}</span>
            </div>
          )}
        </div>

        <Card.Content className="p-4">
          {/* Court Info */}
          <div className="mb-3">
            <h3 className="text-lg font-semibold text-secondary-900 mb-1">{name}</h3>
            {description && (
              <p className="text-sm text-secondary-600 line-clamp-2">{description}</p>
            )}
          </div>

          {/* Court Details */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4 text-sm text-secondary-600">
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{capacity} players</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>${pricePerHour}/hr</span>
              </div>
            </div>
          </div>

          {/* Amenities */}
          {amenities.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-1">
                {amenities.slice(0, 3).map((amenity, index) => (
                  <Badge key={index} variant="default" size="sm">
                    {amenity}
                  </Badge>
                ))}
                {amenities.length > 3 && (
                  <Badge variant="default" size="sm">
                    +{amenities.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          )}

          {/* Action Button */}
          <Button 
            variant={isSelected ? "primary" : "outline"} 
            className="w-full"
            disabled={availability === 'unavailable'}
          >
            {isSelected ? 'Selected' : availability === 'unavailable' ? 'Unavailable' : 'Select Court'}
          </Button>
        </Card.Content>
      </div>
    </Card>
  );
};

export default CourtCard;
