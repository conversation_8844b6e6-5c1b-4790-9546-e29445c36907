import { useState } from 'react';
import { format } from 'date-fns';
import { User, Mail, Phone, CreditCard, Calendar, Clock, MapPin } from 'lucide-react';
import { Card, Input, Button, Select, Badge } from '../ui';

const BookingForm = ({ 
  court, 
  selectedDate, 
  selectedSlot, 
  onSubmit, 
  onCancel,
  loading = false 
}) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    players: selectedSlot?.maxPlayers || 2,
    specialRequests: '',
    paymentMethod: 'card'
  });

  const [errors, setErrors] = useState({});

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
    if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    if (formData.players < 1) newErrors.players = 'At least 1 player required';
    if (formData.players > selectedSlot?.maxPlayers) {
      newErrors.players = `Maximum ${selectedSlot.maxPlayers} players allowed`;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit({
        ...formData,
        court,
        date: selectedDate,
        timeSlot: selectedSlot,
        totalPrice: selectedSlot.price
      });
    }
  };

  if (!court || !selectedDate || !selectedSlot) {
    return (
      <Card>
        <div className="p-6 text-center">
          <p className="text-secondary-600">Please select a court, date, and time slot to continue.</p>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="p-6">
        {/* Booking Summary */}
        <div className="mb-6 p-4 bg-primary-50 rounded-lg">
          <h3 className="text-lg font-semibold text-primary-900 mb-3">Booking Summary</h3>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-primary-600" />
              <span className="font-medium">{court.name}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-primary-600" />
              <span>{format(selectedDate, 'EEEE, MMMM d, yyyy')}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-primary-600" />
              <span>{selectedSlot.startTime} - {selectedSlot.endTime}</span>
            </div>
            <div className="flex items-center justify-between pt-2 border-t border-primary-200">
              <span className="font-medium">Total Price:</span>
              <Badge variant="primary" size="lg">${selectedSlot.price}</Badge>
            </div>
          </div>
        </div>

        {/* Booking Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Personal Information */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3 flex items-center">
              <User className="h-4 w-4 mr-2" />
              Personal Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="First Name"
                value={formData.firstName}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                error={errors.firstName}
                required
              />
              <Input
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                error={errors.lastName}
                required
              />
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3 flex items-center">
              <Mail className="h-4 w-4 mr-2" />
              Contact Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                error={errors.email}
                required
              />
              <Input
                label="Phone Number"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                error={errors.phone}
                required
              />
            </div>
          </div>

          {/* Booking Details */}
          <div>
            <h4 className="text-md font-semibold text-secondary-900 mb-3">Booking Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Number of Players"
                value={formData.players}
                onChange={(e) => handleInputChange('players', parseInt(e.target.value))}
                error={errors.players}
              >
                {Array.from({ length: selectedSlot.maxPlayers }, (_, i) => i + 1).map(num => (
                  <option key={num} value={num}>{num} Player{num > 1 ? 's' : ''}</option>
                ))}
              </Select>
              <Select
                label="Payment Method"
                value={formData.paymentMethod}
                onChange={(e) => handleInputChange('paymentMethod', e.target.value)}
              >
                <option value="card">Credit/Debit Card</option>
                <option value="cash">Pay at Venue</option>
                <option value="paypal">PayPal</option>
              </Select>
            </div>
          </div>

          {/* Special Requests */}
          <div>
            <label className="label text-secondary-700 mb-2 block">
              Special Requests (Optional)
            </label>
            <textarea
              className="input min-h-[80px] resize-none"
              placeholder="Any special requests or notes..."
              value={formData.specialRequests}
              onChange={(e) => handleInputChange('specialRequests', e.target.value)}
              rows={3}
            />
          </div>

          {/* Terms and Conditions */}
          <div className="p-4 bg-secondary-50 rounded-lg">
            <p className="text-sm text-secondary-700">
              By booking this court, you agree to our terms and conditions. 
              Cancellations must be made at least 2 hours before the booking time.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-4 pt-4">
            <Button
              type="submit"
              className="flex-1"
              disabled={loading}
              size="lg"
            >
              {loading ? 'Processing...' : `Book Court - $${selectedSlot.price}`}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
              size="lg"
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </Card>
  );
};

export default BookingForm;
