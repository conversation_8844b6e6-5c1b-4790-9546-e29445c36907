import { useState } from 'react';
import { format } from 'date-fns';
import { Clock, Users } from 'lucide-react';
import { Card, Badge, Button } from '../ui';
import { clsx } from 'clsx';

const TimeSlotGrid = ({ 
  timeSlots = [], 
  selectedSlot, 
  onSlotSelect, 
  courtName,
  selectedDate,
  className = '' 
}) => {
  const groupedSlots = timeSlots.reduce((acc, slot) => {
    const hour = slot.startTime.split(':')[0];
    if (!acc[hour]) acc[hour] = [];
    acc[hour].push(slot);
    return acc;
  }, {});

  const getSlotStatus = (slot) => {
    if (slot.isBooked) return 'booked';
    if (slot.isUnavailable) return 'unavailable';
    return 'available';
  };

  const getSlotConfig = (status) => {
    const configs = {
      available: {
        color: 'bg-white border-secondary-300 text-secondary-900 hover:bg-primary-50 hover:border-primary-300',
        badge: null
      },
      booked: {
        color: 'bg-error-50 border-error-200 text-error-700 cursor-not-allowed',
        badge: { variant: 'error', text: 'Booked' }
      },
      unavailable: {
        color: 'bg-secondary-100 border-secondary-200 text-secondary-500 cursor-not-allowed',
        badge: { variant: 'default', text: 'Unavailable' }
      }
    };
    return configs[status];
  };

  return (
    <Card className={className}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-primary-600" />
            <h3 className="text-lg font-semibold text-secondary-900">Select Time</h3>
          </div>
          {selectedDate && (
            <div className="text-sm text-secondary-600">
              {format(selectedDate, 'EEEE, MMMM d, yyyy')}
            </div>
          )}
        </div>

        {courtName && (
          <div className="mb-4 p-3 bg-primary-50 rounded-lg">
            <div className="text-sm font-medium text-primary-900">{courtName}</div>
          </div>
        )}

        {timeSlots.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-secondary-900 mb-2">No time slots available</h4>
            <p className="text-secondary-600">Please select a different date or court.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {Object.entries(groupedSlots).map(([hour, slots]) => (
              <div key={hour}>
                <div className="text-sm font-medium text-secondary-700 mb-2">
                  {hour}:00 - {parseInt(hour) + 1}:00
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                  {slots.map((slot) => {
                    const status = getSlotStatus(slot);
                    const config = getSlotConfig(status);
                    const isSelected = selectedSlot?.id === slot.id;

                    return (
                      <button
                        key={slot.id}
                        onClick={() => status === 'available' && onSlotSelect(slot)}
                        disabled={status !== 'available'}
                        className={clsx(
                          'p-3 rounded-lg border-2 transition-all duration-200 text-left focus:outline-none focus:ring-2 focus:ring-primary-500',
                          config.color,
                          {
                            'border-primary-600 bg-primary-50': isSelected,
                            'transform scale-105': isSelected,
                          }
                        )}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium">
                            {slot.startTime} - {slot.endTime}
                          </div>
                          {config.badge && (
                            <Badge variant={config.badge.variant} size="sm">
                              {config.badge.text}
                            </Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center justify-between text-sm">
                          <div className="flex items-center space-x-1">
                            <span>${slot.price}</span>
                          </div>
                          {slot.maxPlayers && (
                            <div className="flex items-center space-x-1 text-secondary-600">
                              <Users className="h-3 w-3" />
                              <span>{slot.maxPlayers}</span>
                            </div>
                          )}
                        </div>

                        {slot.bookedBy && (
                          <div className="mt-2 text-xs text-secondary-600">
                            Booked by {slot.bookedBy}
                          </div>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Legend */}
        <div className="mt-6 pt-4 border-t border-secondary-200">
          <div className="flex flex-wrap gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-white border-2 border-secondary-300 rounded"></div>
              <span className="text-secondary-600">Available</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-primary-50 border-2 border-primary-600 rounded"></div>
              <span className="text-secondary-600">Selected</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-error-50 border-2 border-error-200 rounded"></div>
              <span className="text-secondary-600">Booked</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-secondary-100 border-2 border-secondary-200 rounded"></div>
              <span className="text-secondary-600">Unavailable</span>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default TimeSlotGrid;
