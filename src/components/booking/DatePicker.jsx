import { useState } from 'react';
import { format, addDays, startOfWeek, isSameDay, isToday, isBefore } from 'date-fns';
import { ChevronLeft, ChevronRight, Calendar } from 'lucide-react';
import { Button, Card } from '../ui';
import { clsx } from 'clsx';

const DatePicker = ({ selectedDate, onDateSelect, className = '' }) => {
  const [currentWeek, setCurrentWeek] = useState(startOfWeek(new Date()));

  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeek, i));

  const goToPreviousWeek = () => {
    setCurrentWeek(addDays(currentWeek, -7));
  };

  const goToNextWeek = () => {
    setCurrentWeek(addDays(currentWeek, 7));
  };

  const goToToday = () => {
    setCurrentWeek(startOfWeek(new Date()));
  };

  return (
    <Card className={className}>
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Calendar className="h-5 w-5 text-primary-600" />
            <h3 className="text-lg font-semibold text-secondary-900">Select Date</h3>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={goToToday}>
              Today
            </Button>
            <Button variant="ghost" size="sm" onClick={goToPreviousWeek}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="sm" onClick={goToNextWeek}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Week Display */}
        <div className="text-center mb-4">
          <span className="text-sm font-medium text-secondary-600">
            {format(weekDays[0], 'MMM d')} - {format(weekDays[6], 'MMM d, yyyy')}
          </span>
        </div>

        {/* Date Grid */}
        <div className="grid grid-cols-7 gap-2">
          {weekDays.map((date) => {
            const isSelected = selectedDate && isSameDay(date, selectedDate);
            const isCurrentDay = isToday(date);
            const isPast = isBefore(date, new Date()) && !isToday(date);

            return (
              <button
                key={date.toISOString()}
                onClick={() => !isPast && onDateSelect(date)}
                disabled={isPast}
                className={clsx(
                  'p-3 rounded-lg text-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500',
                  {
                    'bg-primary-600 text-white shadow-medium': isSelected,
                    'bg-primary-50 text-primary-700 border border-primary-200': isCurrentDay && !isSelected,
                    'text-secondary-400 cursor-not-allowed': isPast,
                    'text-secondary-700 hover:bg-secondary-100': !isSelected && !isCurrentDay && !isPast,
                  }
                )}
              >
                <div className="text-xs font-medium mb-1">
                  {format(date, 'EEE')}
                </div>
                <div className={clsx('text-lg font-semibold', {
                  'text-white': isSelected,
                  'text-primary-700': isCurrentDay && !isSelected,
                })}>
                  {format(date, 'd')}
                </div>
              </button>
            );
          })}
        </div>
      </div>
    </Card>
  );
};

export default DatePicker;
