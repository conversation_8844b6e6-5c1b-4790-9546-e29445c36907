import { format } from 'date-fns';
import { CheckCircle, Calendar, Clock, MapPin, User, Mail, Phone, Download, Share } from 'lucide-react';
import { Card, But<PERSON>, Badge } from '../ui';

const BookingConfirmation = ({ booking, onClose, onDownload, onShare }) => {
  if (!booking) return null;

  const {
    id,
    court,
    date,
    timeSlot,
    firstName,
    lastName,
    email,
    phone,
    players,
    totalPrice,
    paymentMethod,
    specialRequests,
    status = 'confirmed'
  } = booking;

  return (
    <div className="max-w-2xl mx-auto">
      <Card>
        <div className="p-6">
          {/* Success Header */}
          <div className="text-center mb-6">
            <div className="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-8 w-8 text-success-600" />
            </div>
            <h2 className="text-2xl font-bold text-secondary-900 mb-2">Booking Confirmed!</h2>
            <p className="text-secondary-600">
              Your court has been successfully booked. You'll receive a confirmation email shortly.
            </p>
          </div>

          {/* Booking Details */}
          <div className="space-y-6">
            {/* Booking ID */}
            <div className="text-center p-4 bg-primary-50 rounded-lg">
              <div className="text-sm text-primary-700 mb-1">Booking Reference</div>
              <div className="text-xl font-bold text-primary-900">#{id}</div>
            </div>

            {/* Court and Time Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">{court.name}</div>
                    {court.location && (
                      <div className="text-sm text-secondary-600">{court.location}</div>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">
                      {format(new Date(date), 'EEEE, MMMM d, yyyy')}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">
                      {timeSlot.startTime} - {timeSlot.endTime}
                    </div>
                    <div className="text-sm text-secondary-600">
                      Duration: {timeSlot.duration || '1 hour'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">
                      {firstName} {lastName}
                    </div>
                    <div className="text-sm text-secondary-600">
                      {players} player{players > 1 ? 's' : ''}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">{email}</div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-secondary-500" />
                  <div>
                    <div className="font-medium text-secondary-900">{phone}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Details */}
            <div className="p-4 bg-secondary-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="font-medium text-secondary-900">Payment Details</span>
                <Badge variant="success">Paid</Badge>
              </div>
              <div className="flex items-center justify-between text-sm text-secondary-600 mb-1">
                <span>Court booking</span>
                <span>${totalPrice}</span>
              </div>
              <div className="flex items-center justify-between text-sm text-secondary-600 mb-2">
                <span>Payment method</span>
                <span className="capitalize">{paymentMethod}</span>
              </div>
              <div className="flex items-center justify-between font-medium text-secondary-900 pt-2 border-t border-secondary-200">
                <span>Total</span>
                <span>${totalPrice}</span>
              </div>
            </div>

            {/* Special Requests */}
            {specialRequests && (
              <div className="p-4 bg-warning-50 rounded-lg">
                <div className="font-medium text-warning-900 mb-2">Special Requests</div>
                <p className="text-sm text-warning-800">{specialRequests}</p>
              </div>
            )}

            {/* Important Information */}
            <div className="p-4 bg-primary-50 rounded-lg">
              <h4 className="font-medium text-primary-900 mb-2">Important Information</h4>
              <ul className="text-sm text-primary-800 space-y-1">
                <li>• Please arrive 10 minutes before your booking time</li>
                <li>• Bring appropriate sports attire and equipment</li>
                <li>• Cancellations must be made at least 2 hours in advance</li>
                <li>• Contact us if you need to reschedule your booking</li>
              </ul>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 mt-6 pt-6 border-t border-secondary-200">
            <Button 
              variant="outline" 
              onClick={onDownload}
              className="flex-1"
            >
              <Download className="h-4 w-4 mr-2" />
              Download Receipt
            </Button>
            <Button 
              variant="outline" 
              onClick={onShare}
              className="flex-1"
            >
              <Share className="h-4 w-4 mr-2" />
              Share Booking
            </Button>
            <Button 
              onClick={onClose}
              className="flex-1"
            >
              Done
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default BookingConfirmation;
