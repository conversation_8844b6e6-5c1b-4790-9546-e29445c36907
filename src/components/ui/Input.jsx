import { clsx } from 'clsx';
import { forwardRef } from 'react';

const Input = forwardRef(({ 
  className = '', 
  type = 'text',
  label,
  error,
  ...props 
}, ref) => {
  return (
    <div className="space-y-2">
      {label && (
        <label className="label text-secondary-700">
          {label}
        </label>
      )}
      <input
        type={type}
        className={clsx(
          'input',
          error && 'border-error-500 focus-visible:ring-error-500',
          className
        )}
        ref={ref}
        {...props}
      />
      {error && (
        <p className="text-sm text-error-600">{error}</p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;
