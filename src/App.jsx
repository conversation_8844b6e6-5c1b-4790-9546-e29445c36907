import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AppProvider } from './context/AppContext';
import { ErrorBoundary } from './components/ui';
import Layout from './components/layout/Layout';
import AdminLayout from './components/layout/AdminLayout';

// User Pages
import BookingPage from './pages/BookingPage';
import MyBookingsPage from './pages/MyBookingsPage';

// Admin Pages
import AdminDashboard from './pages/admin/AdminDashboard';
import AdminBookings from './pages/admin/AdminBookings';
import AdminCourts from './pages/admin/AdminCourts';
import AdminSchedule from './pages/admin/AdminSchedule';
import AdminSettings from './pages/admin/AdminSettings';

function App() {
  return (
    <ErrorBoundary>
      <AppProvider>
        <Router>
          <Routes>
            {/* User Routes */}
            <Route path="/" element={
              <Layout>
                <BookingPage />
              </Layout>
            } />
            <Route path="/my-bookings" element={
              <Layout>
                <MyBookingsPage />
              </Layout>
            } />

            {/* Admin Routes */}
            <Route path="/admin" element={
              <AdminLayout>
                <AdminDashboard />
              </AdminLayout>
            } />
            <Route path="/admin/bookings" element={
              <AdminLayout>
                <AdminBookings />
              </AdminLayout>
            } />
            <Route path="/admin/courts" element={
              <AdminLayout>
                <AdminCourts />
              </AdminLayout>
            } />
            <Route path="/admin/schedule" element={
              <AdminLayout>
                <AdminSchedule />
              </AdminLayout>
            } />
            <Route path="/admin/settings" element={
              <AdminLayout>
                <AdminSettings />
              </AdminLayout>
            } />
          </Routes>
        </Router>
      </AppProvider>
    </ErrorBoundary>
  );
}

export default App;
