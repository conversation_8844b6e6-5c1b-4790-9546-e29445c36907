import { useState } from 'react';
import { format, isAfter, isBefore, isToday } from 'date-fns';
import { Calendar, Clock, MapPin, User, Phone, Mail, MoreVertical, Download, Edit, Trash2 } from 'lucide-react';
import { Card, Button, Badge, Modal, Table } from '../components/ui';

// Mock bookings data
const mockBookings = [
  {
    id: 'BK1234567890',
    court: { name: 'Court 1 - Premium', location: 'Main Building' },
    date: '2024-01-25',
    timeSlot: { startTime: '14:00', endTime: '15:00' },
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '****** 567 8900',
    players: 4,
    totalPrice: 45,
    status: 'confirmed',
    createdAt: '2024-01-20T10:00:00Z'
  },
  {
    id: 'BK1234567891',
    court: { name: 'Court 2 - Standard', location: 'Main Building' },
    date: '2024-01-22',
    timeSlot: { startTime: '10:00', endTime: '11:00' },
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '****** 567 8900',
    players: 2,
    totalPrice: 35,
    status: 'completed',
    createdAt: '2024-01-18T15:30:00Z'
  },
  {
    id: 'BK1234567892',
    court: { name: 'Court 3 - Training', location: 'Training Center' },
    date: '2024-01-28',
    timeSlot: { startTime: '16:00', endTime: '17:00' },
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '****** 567 8900',
    players: 4,
    totalPrice: 40,
    status: 'confirmed',
    createdAt: '2024-01-21T09:15:00Z'
  }
];

const MyBookingsPage = () => {
  const [bookings] = useState(mockBookings);
  const [filter, setFilter] = useState('all');
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  const getBookingStatus = (booking) => {
    const bookingDate = new Date(booking.date);
    const now = new Date();

    if (booking.status === 'cancelled') return 'cancelled';
    if (isBefore(bookingDate, now) && !isToday(bookingDate)) return 'completed';
    if (isToday(bookingDate)) return 'today';
    return 'upcoming';
  };

  const getStatusConfig = (status) => {
    const configs = {
      upcoming: { variant: 'primary', text: 'Upcoming' },
      today: { variant: 'warning', text: 'Today' },
      completed: { variant: 'success', text: 'Completed' },
      cancelled: { variant: 'error', text: 'Cancelled' }
    };
    return configs[status] || configs.upcoming;
  };

  const filteredBookings = bookings.filter(booking => {
    if (filter === 'all') return true;
    return getBookingStatus(booking) === filter;
  });

  const handleViewDetails = (booking) => {
    setSelectedBooking(booking);
    setShowDetails(true);
  };

  const handleCancelBooking = (bookingId) => {
    // In a real app, this would make an API call
    console.log('Cancel booking:', bookingId);
  };

  const handleDownloadReceipt = (bookingId) => {
    // In a real app, this would download the receipt
    console.log('Download receipt:', bookingId);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900 mb-2">My Bookings</h1>
        <p className="text-secondary-600">Manage your court reservations</p>
      </div>

      {/* Filter Tabs */}
      <div className="mb-6">
        <div className="flex space-x-1 bg-secondary-100 p-1 rounded-lg w-fit">
          {[
            { key: 'all', label: 'All Bookings' },
            { key: 'upcoming', label: 'Upcoming' },
            { key: 'today', label: 'Today' },
            { key: 'completed', label: 'Completed' }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                filter === tab.key
                  ? 'bg-white text-secondary-900 shadow-sm'
                  : 'text-secondary-600 hover:text-secondary-900'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Bookings List */}
      {filteredBookings.length === 0 ? (
        <Card>
          <div className="p-12 text-center">
            <Calendar className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-secondary-900 mb-2">No bookings found</h3>
            <p className="text-secondary-600 mb-4">
              {filter === 'all'
                ? "You haven't made any bookings yet."
                : `No ${filter} bookings found.`}
            </p>
            <Button onClick={() => window.location.href = '/'}>
              Book a Court
            </Button>
          </div>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredBookings.map(booking => {
            const status = getBookingStatus(booking);
            const statusConfig = getStatusConfig(status);

            return (
              <Card key={booking.id} className="hover:shadow-medium transition-shadow">
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <h3 className="text-lg font-semibold text-secondary-900">
                          {booking.court.name}
                        </h3>
                        <Badge variant={statusConfig.variant}>
                          {statusConfig.text}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-secondary-600">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>{format(new Date(booking.date), 'EEEE, MMM d, yyyy')}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4" />
                          <span>{booking.timeSlot.startTime} - {booking.timeSlot.endTime}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4" />
                          <span>{booking.court.location}</span>
                        </div>
                      </div>

                      <div className="mt-3 flex items-center space-x-4 text-sm text-secondary-600">
                        <div className="flex items-center space-x-1">
                          <User className="h-4 w-4" />
                          <span>{booking.players} players</span>
                        </div>
                        <div className="font-medium text-secondary-900">
                          ${booking.totalPrice}
                        </div>
                        <div>
                          Booking #{booking.id}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(booking)}
                      >
                        View Details
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadReceipt(booking.id)}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      {status === 'upcoming' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCancelBooking(booking.id)}
                          className="text-error-600 hover:text-error-700 hover:border-error-300"
                        >
                          Cancel
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      )}

      {/* Booking Details Modal */}
      <Modal
        isOpen={showDetails}
        onClose={() => setShowDetails(false)}
        title="Booking Details"
        size="lg"
      >
        {selectedBooking && (
          <div className="space-y-6">
            {/* Booking Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Court Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <MapPin className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.court.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-secondary-600">{selectedBooking.court.location}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Date & Time</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-secondary-500" />
                      <span>{format(new Date(selectedBooking.date), 'EEEE, MMMM d, yyyy')}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.timeSlot.startTime} - {selectedBooking.timeSlot.endTime}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Contact Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.firstName} {selectedBooking.lastName}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.email}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-secondary-500" />
                      <span>{selectedBooking.phone}</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-secondary-900 mb-2">Booking Details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-secondary-600">Players:</span>
                      <span>{selectedBooking.players}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-secondary-600">Total Price:</span>
                      <span className="font-medium">${selectedBooking.totalPrice}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-secondary-600">Status:</span>
                      <Badge variant={getStatusConfig(getBookingStatus(selectedBooking)).variant}>
                        {getStatusConfig(getBookingStatus(selectedBooking)).text}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-3 pt-4 border-t border-secondary-200">
              <Button
                variant="outline"
                onClick={() => handleDownloadReceipt(selectedBooking.id)}
                className="flex-1"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Receipt
              </Button>
              {getBookingStatus(selectedBooking) === 'upcoming' && (
                <Button
                  variant="outline"
                  onClick={() => handleCancelBooking(selectedBooking.id)}
                  className="flex-1 text-error-600 hover:text-error-700 hover:border-error-300"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Cancel Booking
                </Button>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default MyBookingsPage;
