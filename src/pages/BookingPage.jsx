import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { ArrowRight, ArrowLeft, CheckCircle } from 'lucide-react';
import { Button, Modal } from '../components/ui';
import { CourtGrid, CourtFilters } from '../components/courts';
import { DatePicker, TimeSlotGrid, BookingForm, BookingConfirmation } from '../components/booking';

// Mock data - will be replaced with real data later
const mockCourts = [
  {
    id: 1,
    name: 'Court 1 - Premium',
    description: 'Professional padel court with premium surface and lighting',
    image: null,
    capacity: 4,
    rating: 4.8,
    pricePerHour: 45,
    amenities: ['WiFi', 'Parking', 'Showers', 'Equipment Rental'],
    availability: 'available',
    location: 'Main Building'
  },
  {
    id: 2,
    name: 'Court 2 - Standard',
    description: 'Standard padel court perfect for casual games',
    image: null,
    capacity: 4,
    rating: 4.5,
    pricePerHour: 35,
    amenities: ['Parking', 'Equipment Rental'],
    availability: 'available',
    location: 'Main Building'
  },
  {
    id: 3,
    name: 'Court 3 - Training',
    description: 'Training court with coaching facilities',
    image: null,
    capacity: 4,
    rating: 4.6,
    pricePerHour: 40,
    amenities: ['WiFi', 'Coaching', 'Equipment Rental'],
    availability: 'busy',
    location: 'Training Center'
  }
];

const generateTimeSlots = (courtId, date) => {
  const slots = [];
  const startHour = 8;
  const endHour = 22;

  for (let hour = startHour; hour < endHour; hour++) {
    const startTime = `${hour.toString().padStart(2, '0')}:00`;
    const endTime = `${(hour + 1).toString().padStart(2, '0')}:00`;

    // Mock some booked slots
    const isBooked = Math.random() < 0.3;
    const isUnavailable = Math.random() < 0.1;

    slots.push({
      id: `${courtId}-${date}-${hour}`,
      startTime,
      endTime,
      price: mockCourts.find(c => c.id === courtId)?.pricePerHour || 40,
      maxPlayers: 4,
      isBooked,
      isUnavailable,
      bookedBy: isBooked ? 'John D.' : null
    });
  }

  return slots;
};

const BookingPage = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedCourt, setSelectedCourt] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedSlot, setSelectedSlot] = useState(null);
  const [timeSlots, setTimeSlots] = useState([]);
  const [filteredCourts, setFilteredCourts] = useState(mockCourts);
  const [booking, setBooking] = useState(null);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [loading, setLoading] = useState(false);

  const steps = [
    { number: 1, title: 'Select Court', description: 'Choose your preferred court' },
    { number: 2, title: 'Pick Date & Time', description: 'Select date and time slot' },
    { number: 3, title: 'Book & Pay', description: 'Complete your booking' }
  ];

  useEffect(() => {
    if (selectedCourt && selectedDate) {
      const slots = generateTimeSlots(selectedCourt.id, format(selectedDate, 'yyyy-MM-dd'));
      setTimeSlots(slots);
    }
  }, [selectedCourt, selectedDate]);

  const handleCourtSelect = (court) => {
    setSelectedCourt(court);
    setCurrentStep(2);
    setSelectedSlot(null);
  };

  const handleDateSelect = (date) => {
    setSelectedDate(date);
    setSelectedSlot(null);
  };

  const handleSlotSelect = (slot) => {
    setSelectedSlot(slot);
    setCurrentStep(3);
  };

  const handleBookingSubmit = async (bookingData) => {
    setLoading(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));

    const newBooking = {
      ...bookingData,
      id: `BK${Date.now()}`,
      status: 'confirmed',
      createdAt: new Date().toISOString()
    };

    setBooking(newBooking);
    setShowConfirmation(true);
    setLoading(false);
  };

  const handleFiltersChange = (filters) => {
    let filtered = [...mockCourts];

    if (filters.availability !== 'all') {
      filtered = filtered.filter(court => court.availability === filters.availability);
    }

    if (filters.rating !== 'all') {
      const minRating = parseFloat(filters.rating);
      filtered = filtered.filter(court => court.rating >= minRating);
    }

    if (filters.amenities.length > 0) {
      filtered = filtered.filter(court =>
        filters.amenities.every(amenity => court.amenities.includes(amenity))
      );
    }

    filtered = filtered.filter(court =>
      court.pricePerHour >= filters.priceMin && court.pricePerHour <= filters.priceMax
    );

    setFilteredCourts(filtered);
  };

  const goToStep = (step) => {
    if (step === 1) {
      setCurrentStep(1);
      setSelectedSlot(null);
    } else if (step === 2 && selectedCourt) {
      setCurrentStep(2);
      setSelectedSlot(null);
    } else if (step === 3 && selectedCourt && selectedDate && selectedSlot) {
      setCurrentStep(3);
    }
  };

  const resetBooking = () => {
    setCurrentStep(1);
    setSelectedCourt(null);
    setSelectedDate(null);
    setSelectedSlot(null);
    setBooking(null);
    setShowConfirmation(false);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-secondary-900 mb-2">Book a Padel Court</h1>
        <p className="text-secondary-600">Select your court, pick a time, and book instantly</p>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-center space-x-8">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-center">
              <button
                onClick={() => goToStep(step.number)}
                className={`flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 ${
                  currentStep === step.number
                    ? 'bg-primary-100 text-primary-700'
                    : currentStep > step.number
                    ? 'bg-success-100 text-success-700 cursor-pointer hover:bg-success-200'
                    : 'bg-secondary-100 text-secondary-500'
                }`}
                disabled={
                  (step.number === 2 && !selectedCourt) ||
                  (step.number === 3 && (!selectedCourt || !selectedDate || !selectedSlot))
                }
              >
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep === step.number
                    ? 'bg-primary-600 text-white'
                    : currentStep > step.number
                    ? 'bg-success-600 text-white'
                    : 'bg-secondary-300 text-secondary-600'
                }`}>
                  {currentStep > step.number ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    step.number
                  )}
                </div>
                <div className="text-left">
                  <div className="font-medium">{step.title}</div>
                  <div className="text-xs opacity-75">{step.description}</div>
                </div>
              </button>
              {index < steps.length - 1 && (
                <ArrowRight className="h-5 w-5 text-secondary-400 mx-4" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Content */}
      <div className="space-y-6">
        {/* Step 1: Court Selection */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <CourtFilters
              onFiltersChange={handleFiltersChange}
              availableAmenities={['WiFi', 'Parking', 'Showers', 'Equipment Rental', 'Coaching']}
              priceRange={{ min: 30, max: 60 }}
            />
            <CourtGrid
              courts={filteredCourts}
              onCourtSelect={handleCourtSelect}
              selectedCourtId={selectedCourt?.id}
            />
          </div>
        )}

        {/* Step 2: Date & Time Selection */}
        {currentStep === 2 && selectedCourt && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <DatePicker
              selectedDate={selectedDate}
              onDateSelect={handleDateSelect}
            />
            <TimeSlotGrid
              timeSlots={timeSlots}
              selectedSlot={selectedSlot}
              onSlotSelect={handleSlotSelect}
              courtName={selectedCourt.name}
              selectedDate={selectedDate}
            />
          </div>
        )}

        {/* Step 3: Booking Form */}
        {currentStep === 3 && selectedCourt && selectedDate && selectedSlot && (
          <BookingForm
            court={selectedCourt}
            selectedDate={selectedDate}
            selectedSlot={selectedSlot}
            onSubmit={handleBookingSubmit}
            onCancel={() => setCurrentStep(2)}
            loading={loading}
          />
        )}
      </div>

      {/* Navigation Buttons */}
      {currentStep > 1 && (
        <div className="mt-8 flex justify-between">
          <Button
            variant="outline"
            onClick={() => setCurrentStep(currentStep - 1)}
            disabled={loading}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
      )}

      {/* Booking Confirmation Modal */}
      <Modal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        size="lg"
      >
        <BookingConfirmation
          booking={booking}
          onClose={resetBooking}
          onDownload={() => console.log('Download receipt')}
          onShare={() => console.log('Share booking')}
        />
      </Modal>
    </div>
  );
};

export default BookingPage;
