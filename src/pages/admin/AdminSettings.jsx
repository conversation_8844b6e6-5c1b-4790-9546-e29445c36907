import { useState } from 'react';
import {
  Save,
  Building,
  Mail,
  Phone,
  MapPin,
  Globe,
  DollarSign,
  Clock,
  BellRing,
  Users,
  Shield,
  Key
} from 'lucide-react';
import { Card, Button, Input, Select, Badge } from '../../components/ui';

const AdminSettings = () => {
  const [businessSettings, setBusinessSettings] = useState({
    name: 'Padel Chase Club',
    email: '<EMAIL>',
    phone: '****** 567 8900',
    address: '123 Padel Court, Sportsville, SP 12345',
    website: 'www.padelchase.com',
    currency: 'USD',
    timeZone: 'America/New_York',
    taxRate: 8.5
  });

  const [bookingSettings, setBookingSettings] = useState({
    minBookingTime: 60, // minutes
    maxBookingTime: 120, // minutes
    advanceBookingDays: 14,
    cancellationPolicy: 'flexible', // flexible, moderate, strict
    cancellationHours: 2,
    refundPercentage: 100,
    allowMultipleBookings: true,
    requirePayment: true,
    sendReminders: true,
    reminderHours: 24
  });

  const [userSettings, setUserSettings] = useState({
    allowGuestBookings: true,
    requirePhoneVerification: false,
    requireEmailVerification: true,
    defaultUserRole: 'customer'
  });

  const handleBusinessSettingChange = (field, value) => {
    setBusinessSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleBookingSettingChange = (field, value) => {
    setBookingSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleUserSettingChange = (field, value) => {
    setUserSettings(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveSettings = () => {
    // In a real app, this would save the settings to the backend
    console.log('Saving settings...');
    console.log('Business settings:', businessSettings);
    console.log('Booking settings:', bookingSettings);
    console.log('User settings:', userSettings);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-secondary-900">Settings</h1>
        <p className="text-secondary-600 mt-1">
          Configure your booking system settings
        </p>
      </div>

      {/* Business Information */}
      <Card>
        <div className="p-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4 flex items-center">
            <Building className="h-5 w-5 mr-2 text-primary-600" />
            Business Information
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Business Name"
              value={businessSettings.name}
              onChange={(e) => handleBusinessSettingChange('name', e.target.value)}
            />
            <Input
              label="Email Address"
              type="email"
              value={businessSettings.email}
              onChange={(e) => handleBusinessSettingChange('email', e.target.value)}
            />
            <Input
              label="Phone Number"
              value={businessSettings.phone}
              onChange={(e) => handleBusinessSettingChange('phone', e.target.value)}
            />
            <Input
              label="Website"
              value={businessSettings.website}
              onChange={(e) => handleBusinessSettingChange('website', e.target.value)}
            />
            <div className="md:col-span-2">
              <Input
                label="Address"
                value={businessSettings.address}
                onChange={(e) => handleBusinessSettingChange('address', e.target.value)}
              />
            </div>
            <Select
              label="Currency"
              value={businessSettings.currency}
              onChange={(e) => handleBusinessSettingChange('currency', e.target.value)}
            >
              <option value="USD">USD ($)</option>
              <option value="EUR">EUR (€)</option>
              <option value="GBP">GBP (£)</option>
              <option value="CAD">CAD ($)</option>
              <option value="AUD">AUD ($)</option>
            </Select>
            <Select
              label="Time Zone"
              value={businessSettings.timeZone}
              onChange={(e) => handleBusinessSettingChange('timeZone', e.target.value)}
            >
              <option value="America/New_York">Eastern Time (ET)</option>
              <option value="America/Chicago">Central Time (CT)</option>
              <option value="America/Denver">Mountain Time (MT)</option>
              <option value="America/Los_Angeles">Pacific Time (PT)</option>
              <option value="Europe/London">London (GMT)</option>
              <option value="Europe/Paris">Paris (CET)</option>
            </Select>
            <Input
              label="Tax Rate (%)"
              type="number"
              value={businessSettings.taxRate}
              onChange={(e) => handleBusinessSettingChange('taxRate', parseFloat(e.target.value))}
            />
          </div>
        </div>
      </Card>

      {/* Booking Settings */}
      <Card>
        <div className="p-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4 flex items-center">
            <Clock className="h-5 w-5 mr-2 text-primary-600" />
            Booking Settings
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              label="Minimum Booking Duration (minutes)"
              type="number"
              value={bookingSettings.minBookingTime}
              onChange={(e) => handleBookingSettingChange('minBookingTime', parseInt(e.target.value))}
            />
            <Input
              label="Maximum Booking Duration (minutes)"
              type="number"
              value={bookingSettings.maxBookingTime}
              onChange={(e) => handleBookingSettingChange('maxBookingTime', parseInt(e.target.value))}
            />
            <Input
              label="Advance Booking Days"
              type="number"
              value={bookingSettings.advanceBookingDays}
              onChange={(e) => handleBookingSettingChange('advanceBookingDays', parseInt(e.target.value))}
            />
            <Select
              label="Cancellation Policy"
              value={bookingSettings.cancellationPolicy}
              onChange={(e) => handleBookingSettingChange('cancellationPolicy', e.target.value)}
            >
              <option value="flexible">Flexible (Full refund if cancelled in time)</option>
              <option value="moderate">Moderate (Partial refund if cancelled in time)</option>
              <option value="strict">Strict (No refund)</option>
            </Select>
            <Input
              label="Cancellation Hours Before"
              type="number"
              value={bookingSettings.cancellationHours}
              onChange={(e) => handleBookingSettingChange('cancellationHours', parseInt(e.target.value))}
            />
            {bookingSettings.cancellationPolicy === 'moderate' && (
              <Input
                label="Refund Percentage (%)"
                type="number"
                value={bookingSettings.refundPercentage}
                onChange={(e) => handleBookingSettingChange('refundPercentage', parseInt(e.target.value))}
              />
            )}
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="allowMultipleBookings"
                checked={bookingSettings.allowMultipleBookings}
                onChange={(e) => handleBookingSettingChange('allowMultipleBookings', e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="allowMultipleBookings" className="text-sm font-medium text-secondary-700">
                Allow users to make multiple bookings on the same day
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="requirePayment"
                checked={bookingSettings.requirePayment}
                onChange={(e) => handleBookingSettingChange('requirePayment', e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="requirePayment" className="text-sm font-medium text-secondary-700">
                Require payment to confirm booking
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="sendReminders"
                checked={bookingSettings.sendReminders}
                onChange={(e) => handleBookingSettingChange('sendReminders', e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="sendReminders" className="text-sm font-medium text-secondary-700">
                Send booking reminders
              </label>
            </div>
            {bookingSettings.sendReminders && (
              <Input
                label="Reminder Hours Before"
                type="number"
                value={bookingSettings.reminderHours}
                onChange={(e) => handleBookingSettingChange('reminderHours', parseInt(e.target.value))}
              />
            )}
          </div>
        </div>
      </Card>

      {/* User Settings */}
      <Card>
        <div className="p-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4 flex items-center">
            <Users className="h-5 w-5 mr-2 text-primary-600" />
            User Settings
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="allowGuestBookings"
                checked={userSettings.allowGuestBookings}
                onChange={(e) => handleUserSettingChange('allowGuestBookings', e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="allowGuestBookings" className="text-sm font-medium text-secondary-700">
                Allow guest bookings (without registration)
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="requireEmailVerification"
                checked={userSettings.requireEmailVerification}
                onChange={(e) => handleUserSettingChange('requireEmailVerification', e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="requireEmailVerification" className="text-sm font-medium text-secondary-700">
                Require email verification for new accounts
              </label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="requirePhoneVerification"
                checked={userSettings.requirePhoneVerification}
                onChange={(e) => handleUserSettingChange('requirePhoneVerification', e.target.checked)}
                className="rounded border-secondary-300 text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="requirePhoneVerification" className="text-sm font-medium text-secondary-700">
                Require phone verification for new accounts
              </label>
            </div>
            <Select
              label="Default User Role"
              value={userSettings.defaultUserRole}
              onChange={(e) => handleUserSettingChange('defaultUserRole', e.target.value)}
            >
              <option value="customer">Customer</option>
              <option value="member">Member</option>
              <option value="vip">VIP</option>
            </Select>
          </div>
        </div>
      </Card>

      {/* Admin Access */}
      <Card>
        <div className="p-6">
          <h2 className="text-xl font-semibold text-secondary-900 mb-4 flex items-center">
            <Shield className="h-5 w-5 mr-2 text-primary-600" />
            Admin Access
          </h2>
          <div className="bg-warning-50 border border-warning-200 rounded-lg p-4 mb-4">
            <div className="flex items-start">
              <Key className="h-5 w-5 text-warning-600 mt-0.5 mr-2" />
              <div>
                <h3 className="font-medium text-warning-800">Admin Access Control</h3>
                <p className="text-sm text-warning-700">
                  This section will allow you to manage admin users and their permissions.
                  For security reasons, this feature will be implemented in the backend.
                </p>
              </div>
            </div>
          </div>
          <div className="text-center">
            <Button variant="outline" disabled>
              Manage Admin Users
            </Button>
          </div>
        </div>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSaveSettings} size="lg">
          <Save className="h-4 w-4 mr-2" />
          Save All Settings
        </Button>
      </div>
    </div>
  );
};

export default AdminSettings;
