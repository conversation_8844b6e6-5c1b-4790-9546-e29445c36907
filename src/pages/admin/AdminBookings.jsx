import { useState } from 'react';
import { format } from 'date-fns';
import {
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  Plus,
  Calendar,
  Clock,
  MapPin,
  User,
  Phone,
  Mail,
  MoreVertical
} from 'lucide-react';
import { Card, Button, Input, Select, Badge, Table, Modal } from '../../components/ui';

// Mock bookings data
const mockBookings = [
  {
    id: 'BK1234567890',
    court: { id: 1, name: 'Court 1 - Premium', location: 'Main Building' },
    customer: {
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '****** 567 8900'
    },
    date: '2024-01-25',
    timeSlot: { startTime: '14:00', endTime: '15:00' },
    players: 4,
    totalPrice: 45,
    status: 'confirmed',
    paymentStatus: 'paid',
    createdAt: '2024-01-20T10:00:00Z',
    specialRequests: 'Please prepare equipment for beginners'
  },
  {
    id: 'BK1234567891',
    court: { id: 2, name: 'Court 2 - Standard', location: 'Main Building' },
    customer: {
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      email: '<EMAIL>',
      phone: '****** 567 8901'
    },
    date: '2024-01-25',
    timeSlot: { startTime: '16:00', endTime: '17:00' },
    players: 2,
    totalPrice: 35,
    status: 'pending',
    paymentStatus: 'pending',
    createdAt: '2024-01-21T15:30:00Z',
    specialRequests: null
  },
  {
    id: 'BK1234567892',
    court: { id: 3, name: 'Court 3 - Training', location: 'Training Center' },
    customer: {
      firstName: 'Mike',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '****** 567 8902'
    },
    date: '2024-01-26',
    timeSlot: { startTime: '10:00', endTime: '11:00' },
    players: 4,
    totalPrice: 40,
    status: 'confirmed',
    paymentStatus: 'paid',
    createdAt: '2024-01-22T09:15:00Z',
    specialRequests: null
  },
  {
    id: 'BK1234567893',
    court: { id: 1, name: 'Court 1 - Premium', location: 'Main Building' },
    customer: {
      firstName: 'Sarah',
      lastName: 'Wilson',
      email: '<EMAIL>',
      phone: '****** 567 8903'
    },
    date: '2024-01-24',
    timeSlot: { startTime: '18:00', endTime: '19:00' },
    players: 2,
    totalPrice: 45,
    status: 'cancelled',
    paymentStatus: 'refunded',
    createdAt: '2024-01-19T14:20:00Z',
    specialRequests: null
  }
];

const AdminBookings = () => {
  const [bookings] = useState(mockBookings);
  const [filteredBookings, setFilteredBookings] = useState(mockBookings);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [courtFilter, setCourtFilter] = useState('all');
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  const getStatusConfig = (status) => {
    const configs = {
      confirmed: { variant: 'success', text: 'Confirmed' },
      pending: { variant: 'warning', text: 'Pending' },
      cancelled: { variant: 'error', text: 'Cancelled' },
      completed: { variant: 'default', text: 'Completed' }
    };
    return configs[status] || configs.pending;
  };

  const getPaymentStatusConfig = (status) => {
    const configs = {
      paid: { variant: 'success', text: 'Paid' },
      pending: { variant: 'warning', text: 'Pending' },
      refunded: { variant: 'error', text: 'Refunded' },
      failed: { variant: 'error', text: 'Failed' }
    };
    return configs[status] || configs.pending;
  };

  // Filter bookings based on search and filters
  const applyFilters = () => {
    let filtered = [...bookings];

    if (searchTerm) {
      filtered = filtered.filter(booking =>
        booking.customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        booking.id.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(booking => booking.status === statusFilter);
    }

    if (courtFilter !== 'all') {
      filtered = filtered.filter(booking => booking.court.id === parseInt(courtFilter));
    }

    setFilteredBookings(filtered);
  };

  // Apply filters when dependencies change
  useState(() => {
    applyFilters();
  }, [searchTerm, statusFilter, courtFilter]);

  const handleViewDetails = (booking) => {
    setSelectedBooking(booking);
    setShowDetails(true);
  };

  const handleUpdateStatus = (bookingId, newStatus) => {
    // In a real app, this would make an API call
    console.log('Update booking status:', bookingId, newStatus);
  };

  const handleDeleteBooking = (bookingId) => {
    // In a real app, this would make an API call
    console.log('Delete booking:', bookingId);
  };

  const handleExport = () => {
    // In a real app, this would export the data
    console.log('Export bookings');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-secondary-900">Manage Bookings</h1>
          <p className="text-secondary-600 mt-1">
            View and manage all court bookings
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Booking
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-secondary-500" />
                <Input
                  placeholder="Search bookings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>

          {showFilters && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-secondary-200">
              <Select
                label="Status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="confirmed">Confirmed</option>
                <option value="pending">Pending</option>
                <option value="cancelled">Cancelled</option>
                <option value="completed">Completed</option>
              </Select>

              <Select
                label="Court"
                value={courtFilter}
                onChange={(e) => setCourtFilter(e.target.value)}
              >
                <option value="all">All Courts</option>
                <option value="1">Court 1 - Premium</option>
                <option value="2">Court 2 - Standard</option>
                <option value="3">Court 3 - Training</option>
              </Select>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('all');
                    setCourtFilter('all');
                  }}
                  className="w-full"
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Bookings Table */}
      <Card>
        <div className="overflow-x-auto">
          <Table>
            <Table.Header>
              <Table.Row>
                <Table.Head>Booking ID</Table.Head>
                <Table.Head>Customer</Table.Head>
                <Table.Head>Court</Table.Head>
                <Table.Head>Date & Time</Table.Head>
                <Table.Head>Players</Table.Head>
                <Table.Head>Amount</Table.Head>
                <Table.Head>Status</Table.Head>
                <Table.Head>Payment</Table.Head>
                <Table.Head>Actions</Table.Head>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {filteredBookings.map((booking) => (
                <Table.Row key={booking.id}>
                  <Table.Cell>
                    <div className="font-medium text-primary-600">
                      {booking.id}
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      <div className="font-medium text-secondary-900">
                        {booking.customer.firstName} {booking.customer.lastName}
                      </div>
                      <div className="text-sm text-secondary-600">
                        {booking.customer.email}
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      <div className="font-medium text-secondary-900">
                        {booking.court.name}
                      </div>
                      <div className="text-sm text-secondary-600">
                        {booking.court.location}
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <div>
                      <div className="font-medium text-secondary-900">
                        {format(new Date(booking.date), 'MMM d, yyyy')}
                      </div>
                      <div className="text-sm text-secondary-600">
                        {booking.timeSlot.startTime} - {booking.timeSlot.endTime}
                      </div>
                    </div>
                  </Table.Cell>
                  <Table.Cell>
                    <span className="font-medium">{booking.players}</span>
                  </Table.Cell>
                  <Table.Cell>
                    <span className="font-medium">${booking.totalPrice}</span>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge variant={getStatusConfig(booking.status).variant}>
                      {getStatusConfig(booking.status).text}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <Badge variant={getPaymentStatusConfig(booking.paymentStatus).variant}>
                      {getPaymentStatusConfig(booking.paymentStatus).text}
                    </Badge>
                  </Table.Cell>
                  <Table.Cell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDetails(booking)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => console.log('Edit booking:', booking.id)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteBooking(booking.id)}
                        className="text-error-600 hover:text-error-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        </div>

        {filteredBookings.length === 0 && (
          <div className="p-12 text-center">
            <Calendar className="h-12 w-12 text-secondary-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-secondary-900 mb-2">No bookings found</h3>
            <p className="text-secondary-600">
              {searchTerm || statusFilter !== 'all' || courtFilter !== 'all'
                ? 'Try adjusting your search or filters.'
                : 'No bookings have been made yet.'}
            </p>
          </div>
        )}
      </Card>

      {/* Booking Details Modal */}
      <Modal
        isOpen={showDetails}
        onClose={() => setShowDetails(false)}
        title="Booking Details"
        size="lg"
      >
        {selectedBooking && (
          <div className="space-y-6">
            {/* Booking Header */}
            <div className="flex items-center justify-between p-4 bg-primary-50 rounded-lg">
              <div>
                <h3 className="text-lg font-semibold text-primary-900">
                  Booking #{selectedBooking.id}
                </h3>
                <p className="text-sm text-primary-700">
                  Created on {format(new Date(selectedBooking.createdAt), 'MMM d, yyyy')}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={getStatusConfig(selectedBooking.status).variant}>
                  {getStatusConfig(selectedBooking.status).text}
                </Badge>
                <Badge variant={getPaymentStatusConfig(selectedBooking.paymentStatus).variant}>
                  {getPaymentStatusConfig(selectedBooking.paymentStatus).text}
                </Badge>
              </div>
            </div>

            {/* Booking Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-secondary-900 flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  Customer Information
                </h4>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-secondary-600">Name:</span>
                    <span className="ml-2 font-medium">
                      {selectedBooking.customer.firstName} {selectedBooking.customer.lastName}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-secondary-500 mr-2" />
                    <span>{selectedBooking.customer.email}</span>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 text-secondary-500 mr-2" />
                    <span>{selectedBooking.customer.phone}</span>
                  </div>
                </div>
              </div>

              {/* Booking Information */}
              <div className="space-y-4">
                <h4 className="font-medium text-secondary-900 flex items-center">
                  <Calendar className="h-4 w-4 mr-2" />
                  Booking Information
                </h4>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 text-secondary-500 mr-2" />
                    <div>
                      <div className="font-medium">{selectedBooking.court.name}</div>
                      <div className="text-secondary-600">{selectedBooking.court.location}</div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-secondary-500 mr-2" />
                    <span>{format(new Date(selectedBooking.date), 'EEEE, MMMM d, yyyy')}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-secondary-500 mr-2" />
                    <span>{selectedBooking.timeSlot.startTime} - {selectedBooking.timeSlot.endTime}</span>
                  </div>
                  <div>
                    <span className="text-secondary-600">Players:</span>
                    <span className="ml-2 font-medium">{selectedBooking.players}</span>
                  </div>
                  <div>
                    <span className="text-secondary-600">Total Amount:</span>
                    <span className="ml-2 font-medium">${selectedBooking.totalPrice}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Special Requests */}
            {selectedBooking.specialRequests && (
              <div className="p-4 bg-warning-50 rounded-lg">
                <h4 className="font-medium text-warning-900 mb-2">Special Requests</h4>
                <p className="text-sm text-warning-800">{selectedBooking.specialRequests}</p>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-3 pt-4 border-t border-secondary-200">
              <Button
                variant="outline"
                onClick={() => handleUpdateStatus(selectedBooking.id, 'confirmed')}
                disabled={selectedBooking.status === 'confirmed'}
                className="flex-1"
              >
                Confirm Booking
              </Button>
              <Button
                variant="outline"
                onClick={() => handleUpdateStatus(selectedBooking.id, 'cancelled')}
                disabled={selectedBooking.status === 'cancelled'}
                className="flex-1 text-error-600 hover:text-error-700"
              >
                Cancel Booking
              </Button>
              <Button
                onClick={() => console.log('Edit booking:', selectedBooking.id)}
                className="flex-1"
              >
                Edit Booking
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AdminBookings;
