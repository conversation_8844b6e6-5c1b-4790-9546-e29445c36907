import { format, addDays, subDays } from 'date-fns';

// Mock Courts Data
export const mockCourts = [
  {
    id: 1,
    name: 'Court 1 - Premium',
    description: 'Professional padel court with premium surface and lighting. Perfect for competitive play with high-quality artificial grass and LED professional lighting system.',
    image: null,
    images: [],
    location: 'Main Building',
    capacity: 4,
    rating: 4.8,
    pricePerHour: 45,
    amenities: ['WiFi', 'Parking', 'Showers', 'Equipment Rental', 'Coaching'],
    availability: 'available',
    status: 'active',
    specifications: {
      surface: 'Artificial Grass Premium',
      lighting: 'LED Professional',
      size: '20m x 10m',
      walls: 'Tempered Glass',
      ceiling: '6m height',
      flooring: 'Non-slip surface'
    },
    rules: [
      'Maximum 4 players per booking',
      'Proper sports attire required',
      'No food or drinks on court',
      'Equipment must be returned after use',
      'Court must be cleaned after use'
    ],
    maintenanceSchedule: 'Weekly',
    lastMaintenance: '2024-01-15',
    nextMaintenance: '2024-01-22'
  },
  {
    id: 2,
    name: 'Court 2 - Standard',
    description: 'Standard padel court perfect for casual games and recreational play. Features quality artificial grass and standard LED lighting.',
    image: null,
    images: [],
    location: 'Main Building',
    capacity: 4,
    rating: 4.5,
    pricePerHour: 35,
    amenities: ['Parking', 'Equipment Rental', 'Coaching'],
    availability: 'available',
    status: 'active',
    specifications: {
      surface: 'Artificial Grass Standard',
      lighting: 'LED Standard',
      size: '20m x 10m',
      walls: 'Tempered Glass',
      ceiling: '6m height',
      flooring: 'Standard surface'
    },
    rules: [
      'Maximum 4 players per booking',
      'Proper sports attire required',
      'No food or drinks on court',
      'Equipment must be returned after use'
    ],
    maintenanceSchedule: 'Bi-weekly',
    lastMaintenance: '2024-01-10',
    nextMaintenance: '2024-01-24'
  },
  {
    id: 3,
    name: 'Court 3 - Training',
    description: 'Specialized training court with coaching facilities and video analysis equipment. Ideal for lessons and skill development.',
    image: null,
    images: [],
    location: 'Training Center',
    capacity: 4,
    rating: 4.6,
    pricePerHour: 40,
    amenities: ['WiFi', 'Coaching', 'Equipment Rental', 'Video Analysis'],
    availability: 'busy',
    status: 'active',
    specifications: {
      surface: 'Artificial Grass Training',
      lighting: 'LED Professional',
      size: '20m x 10m',
      walls: 'Tempered Glass',
      ceiling: '6m height',
      flooring: 'Training surface'
    },
    rules: [
      'Maximum 4 players per booking',
      'Coaching sessions have priority',
      'Video equipment must be handled by staff',
      'Proper sports attire required'
    ],
    maintenanceSchedule: 'Weekly',
    lastMaintenance: '2024-01-20',
    nextMaintenance: '2024-01-27'
  },
  {
    id: 4,
    name: 'Court 4 - VIP',
    description: 'Luxury VIP court with premium amenities including private changing rooms, refreshment area, and concierge service.',
    image: null,
    images: [],
    location: 'VIP Section',
    capacity: 4,
    rating: 4.9,
    pricePerHour: 65,
    amenities: ['WiFi', 'Parking', 'Showers', 'Equipment Rental', 'Coaching', 'Cafe', 'Private Lounge'],
    availability: 'available',
    status: 'active',
    specifications: {
      surface: 'Artificial Grass Premium Plus',
      lighting: 'LED Professional Plus',
      size: '20m x 10m',
      walls: 'Tempered Glass Premium',
      ceiling: '7m height',
      flooring: 'Premium non-slip surface'
    },
    rules: [
      'Maximum 4 players per booking',
      'VIP dress code enforced',
      'Complimentary refreshments included',
      'Personal equipment storage available',
      'Priority booking for members'
    ],
    maintenanceSchedule: 'Daily',
    lastMaintenance: '2024-01-24',
    nextMaintenance: '2024-01-25'
  }
];

// Generate mock bookings
const generateMockBookings = () => {
  const bookings = [];
  const statuses = ['confirmed', 'pending', 'cancelled', 'completed'];
  const paymentStatuses = ['paid', 'pending', 'refunded', 'failed'];
  
  const customers = [
    { firstName: 'John', lastName: 'Doe', email: '<EMAIL>', phone: '****** 567 8900' },
    { firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>', phone: '****** 567 8901' },
    { firstName: 'Mike', lastName: 'Johnson', email: '<EMAIL>', phone: '****** 567 8902' },
    { firstName: 'Sarah', lastName: 'Wilson', email: '<EMAIL>', phone: '****** 567 8903' },
    { firstName: 'David', lastName: 'Brown', email: '<EMAIL>', phone: '****** 567 8904' },
    { firstName: 'Lisa', lastName: 'Davis', email: '<EMAIL>', phone: '****** 567 8905' },
    { firstName: 'Tom', lastName: 'Miller', email: '<EMAIL>', phone: '****** 567 8906' },
    { firstName: 'Emma', lastName: 'Garcia', email: '<EMAIL>', phone: '****** 567 8907' }
  ];

  const specialRequests = [
    'Please prepare equipment for beginners',
    'Need extra towels',
    'Birthday celebration - please prepare decorations',
    'Corporate event - need invoice',
    'First time playing - need basic instruction',
    null,
    null,
    null
  ];

  // Generate bookings for the past 30 days and next 30 days
  for (let i = -30; i <= 30; i++) {
    const date = format(addDays(new Date(), i), 'yyyy-MM-dd');
    const numBookings = Math.floor(Math.random() * 4) + 1; // 1-4 bookings per day
    
    for (let j = 0; j < numBookings; j++) {
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const court = mockCourts[Math.floor(Math.random() * mockCourts.length)];
      const hour = Math.floor(Math.random() * 12) + 8; // 8 AM to 8 PM
      const startTime = `${hour.toString().padStart(2, '0')}:00`;
      const endTime = `${(hour + 1).toString().padStart(2, '0')}:00`;
      
      let status = statuses[Math.floor(Math.random() * statuses.length)];
      let paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
      
      // Adjust status based on date
      if (i < -1) status = 'completed';
      if (i > 14) status = Math.random() > 0.7 ? 'pending' : 'confirmed';
      
      // Adjust payment status based on booking status
      if (status === 'completed' || status === 'confirmed') paymentStatus = 'paid';
      if (status === 'cancelled') paymentStatus = Math.random() > 0.5 ? 'refunded' : 'paid';
      
      bookings.push({
        id: `BK${Date.now()}${Math.random().toString(36).substr(2, 9)}`,
        court,
        customer,
        date,
        timeSlot: { startTime, endTime, duration: '1 hour' },
        players: Math.floor(Math.random() * 3) + 2, // 2-4 players
        totalPrice: court.pricePerHour,
        status,
        paymentStatus,
        paymentMethod: Math.random() > 0.5 ? 'card' : 'cash',
        specialRequests: specialRequests[Math.floor(Math.random() * specialRequests.length)],
        createdAt: subDays(new Date(date), Math.floor(Math.random() * 7)).toISOString()
      });
    }
  }
  
  return bookings.slice(0, 50); // Limit to 50 bookings for demo
};

export const mockBookings = generateMockBookings();

// Mock Operating Hours
export const mockOperatingHours = {
  monday: { open: '08:00', close: '22:00', isOpen: true },
  tuesday: { open: '08:00', close: '22:00', isOpen: true },
  wednesday: { open: '08:00', close: '22:00', isOpen: true },
  thursday: { open: '08:00', close: '22:00', isOpen: true },
  friday: { open: '08:00', close: '23:00', isOpen: true },
  saturday: { open: '09:00', close: '23:00', isOpen: true },
  sunday: { open: '09:00', close: '20:00', isOpen: true }
};

// Mock Holidays
export const mockHolidays = [
  {
    id: 1,
    date: '2024-01-01',
    name: 'New Year\'s Day',
    isFullDay: true,
    hours: null
  },
  {
    id: 2,
    date: '2024-07-04',
    name: 'Independence Day',
    isFullDay: true,
    hours: null
  },
  {
    id: 3,
    date: '2024-12-25',
    name: 'Christmas Day',
    isFullDay: true,
    hours: null
  },
  {
    id: 4,
    date: '2024-12-31',
    name: 'New Year\'s Eve',
    isFullDay: false,
    hours: { open: '08:00', close: '18:00' }
  },
  {
    id: 5,
    date: '2024-11-28',
    name: 'Thanksgiving Day',
    isFullDay: false,
    hours: { open: '10:00', close: '16:00' }
  }
];

// Mock Time Slots Generator
export const generateTimeSlots = (courtId, date, operatingHours = mockOperatingHours) => {
  const dayOfWeek = format(new Date(date), 'EEEE').toLowerCase();
  const dayHours = operatingHours[dayOfWeek];
  
  if (!dayHours || !dayHours.isOpen) {
    return [];
  }
  
  const slots = [];
  const startHour = parseInt(dayHours.open.split(':')[0]);
  const endHour = parseInt(dayHours.close.split(':')[0]);
  
  for (let hour = startHour; hour < endHour; hour++) {
    const startTime = `${hour.toString().padStart(2, '0')}:00`;
    const endTime = `${(hour + 1).toString().padStart(2, '0')}:00`;
    
    // Check if slot is booked
    const isBooked = mockBookings.some(booking => 
      booking.court.id === courtId &&
      booking.date === date &&
      booking.timeSlot.startTime === startTime &&
      (booking.status === 'confirmed' || booking.status === 'pending')
    );
    
    const bookedBooking = mockBookings.find(booking => 
      booking.court.id === courtId &&
      booking.date === date &&
      booking.timeSlot.startTime === startTime &&
      (booking.status === 'confirmed' || booking.status === 'pending')
    );
    
    // Mock some unavailable slots
    const isUnavailable = !isBooked && Math.random() < 0.1;
    
    const court = mockCourts.find(c => c.id === courtId);
    
    slots.push({
      id: `${courtId}-${date}-${hour}`,
      startTime,
      endTime,
      price: court?.pricePerHour || 40,
      maxPlayers: 4,
      isBooked,
      isUnavailable,
      bookedBy: bookedBooking ? `${bookedBooking.customer.firstName} ${bookedBooking.customer.lastName.charAt(0)}.` : null
    });
  }
  
  return slots;
};

// Available amenities for filtering
export const availableAmenities = [
  'WiFi',
  'Parking',
  'Showers',
  'Equipment Rental',
  'Coaching',
  'Cafe',
  'Private Lounge',
  'Video Analysis'
];

// Mock user data
export const mockUser = {
  id: 1,
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '****** 567 8900',
  role: 'customer',
  isVerified: true,
  createdAt: '2024-01-01T00:00:00Z',
  preferences: {
    notifications: true,
    newsletter: true,
    reminders: true
  }
};
