import { createContext, useContext, useReducer, useEffect } from 'react';
import { mockCourts, mockBookings, mockOperatingHours, mockHolidays } from '../data/mockData';

// Initial state
const initialState = {
  courts: mockCourts,
  bookings: mockBookings,
  operatingHours: mockOperatingHours,
  holidays: mockHolidays,
  user: null,
  loading: false,
  error: null,
  filters: {
    courtFilters: {
      availability: 'all',
      priceMin: 0,
      priceMax: 100,
      amenities: [],
      rating: 'all'
    },
    bookingFilters: {
      status: 'all',
      court: 'all',
      dateRange: null
    }
  }
};

// Action types
const ActionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR',
  SET_USER: 'SET_USER',
  
  // Courts
  SET_COURTS: 'SET_COURTS',
  ADD_COURT: 'ADD_COURT',
  UPDATE_COURT: 'UPDATE_COURT',
  DELETE_COURT: 'DELETE_COURT',
  
  // Bookings
  SET_BOOKINGS: 'SET_BOOKINGS',
  ADD_BOOKING: 'ADD_BOOKING',
  UPDATE_BOOKING: 'UPDATE_BOOKING',
  DELETE_BOOKING: 'DELETE_BOOKING',
  
  // Operating Hours
  UPDATE_OPERATING_HOURS: 'UPDATE_OPERATING_HOURS',
  
  // Holidays
  ADD_HOLIDAY: 'ADD_HOLIDAY',
  DELETE_HOLIDAY: 'DELETE_HOLIDAY',
  
  // Filters
  SET_COURT_FILTERS: 'SET_COURT_FILTERS',
  SET_BOOKING_FILTERS: 'SET_BOOKING_FILTERS',
  RESET_FILTERS: 'RESET_FILTERS'
};

// Reducer function
const appReducer = (state, action) => {
  switch (action.type) {
    case ActionTypes.SET_LOADING:
      return { ...state, loading: action.payload };
      
    case ActionTypes.SET_ERROR:
      return { ...state, error: action.payload, loading: false };
      
    case ActionTypes.SET_USER:
      return { ...state, user: action.payload };
      
    // Courts
    case ActionTypes.SET_COURTS:
      return { ...state, courts: action.payload };
      
    case ActionTypes.ADD_COURT:
      return { ...state, courts: [...state.courts, action.payload] };
      
    case ActionTypes.UPDATE_COURT:
      return {
        ...state,
        courts: state.courts.map(court =>
          court.id === action.payload.id ? { ...court, ...action.payload } : court
        )
      };
      
    case ActionTypes.DELETE_COURT:
      return {
        ...state,
        courts: state.courts.filter(court => court.id !== action.payload)
      };
      
    // Bookings
    case ActionTypes.SET_BOOKINGS:
      return { ...state, bookings: action.payload };
      
    case ActionTypes.ADD_BOOKING:
      return { ...state, bookings: [...state.bookings, action.payload] };
      
    case ActionTypes.UPDATE_BOOKING:
      return {
        ...state,
        bookings: state.bookings.map(booking =>
          booking.id === action.payload.id ? { ...booking, ...action.payload } : booking
        )
      };
      
    case ActionTypes.DELETE_BOOKING:
      return {
        ...state,
        bookings: state.bookings.filter(booking => booking.id !== action.payload)
      };
      
    // Operating Hours
    case ActionTypes.UPDATE_OPERATING_HOURS:
      return { ...state, operatingHours: action.payload };
      
    // Holidays
    case ActionTypes.ADD_HOLIDAY:
      return { ...state, holidays: [...state.holidays, action.payload] };
      
    case ActionTypes.DELETE_HOLIDAY:
      return {
        ...state,
        holidays: state.holidays.filter(holiday => holiday.id !== action.payload)
      };
      
    // Filters
    case ActionTypes.SET_COURT_FILTERS:
      return {
        ...state,
        filters: {
          ...state.filters,
          courtFilters: { ...state.filters.courtFilters, ...action.payload }
        }
      };
      
    case ActionTypes.SET_BOOKING_FILTERS:
      return {
        ...state,
        filters: {
          ...state.filters,
          bookingFilters: { ...state.filters.bookingFilters, ...action.payload }
        }
      };
      
    case ActionTypes.RESET_FILTERS:
      return {
        ...state,
        filters: initialState.filters
      };
      
    default:
      return state;
  }
};

// Create context
const AppContext = createContext();

// Context provider component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Action creators
  const actions = {
    setLoading: (loading) => dispatch({ type: ActionTypes.SET_LOADING, payload: loading }),
    setError: (error) => dispatch({ type: ActionTypes.SET_ERROR, payload: error }),
    setUser: (user) => dispatch({ type: ActionTypes.SET_USER, payload: user }),
    
    // Courts
    setCourts: (courts) => dispatch({ type: ActionTypes.SET_COURTS, payload: courts }),
    addCourt: (court) => dispatch({ type: ActionTypes.ADD_COURT, payload: court }),
    updateCourt: (court) => dispatch({ type: ActionTypes.UPDATE_COURT, payload: court }),
    deleteCourt: (courtId) => dispatch({ type: ActionTypes.DELETE_COURT, payload: courtId }),
    
    // Bookings
    setBookings: (bookings) => dispatch({ type: ActionTypes.SET_BOOKINGS, payload: bookings }),
    addBooking: (booking) => dispatch({ type: ActionTypes.ADD_BOOKING, payload: booking }),
    updateBooking: (booking) => dispatch({ type: ActionTypes.UPDATE_BOOKING, payload: booking }),
    deleteBooking: (bookingId) => dispatch({ type: ActionTypes.DELETE_BOOKING, payload: bookingId }),
    
    // Operating Hours
    updateOperatingHours: (hours) => dispatch({ type: ActionTypes.UPDATE_OPERATING_HOURS, payload: hours }),
    
    // Holidays
    addHoliday: (holiday) => dispatch({ type: ActionTypes.ADD_HOLIDAY, payload: holiday }),
    deleteHoliday: (holidayId) => dispatch({ type: ActionTypes.DELETE_HOLIDAY, payload: holidayId }),
    
    // Filters
    setCourtFilters: (filters) => dispatch({ type: ActionTypes.SET_COURT_FILTERS, payload: filters }),
    setBookingFilters: (filters) => dispatch({ type: ActionTypes.SET_BOOKING_FILTERS, payload: filters }),
    resetFilters: () => dispatch({ type: ActionTypes.RESET_FILTERS })
  };

  // Persist state to localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('padelBookingState');
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        // Only restore certain parts of the state
        if (parsedState.courts) actions.setCourts(parsedState.courts);
        if (parsedState.bookings) actions.setBookings(parsedState.bookings);
        if (parsedState.operatingHours) actions.updateOperatingHours(parsedState.operatingHours);
        if (parsedState.holidays) actions.setHolidays(parsedState.holidays);
      } catch (error) {
        console.error('Error loading saved state:', error);
      }
    }
  }, []);

  // Save state to localStorage when it changes
  useEffect(() => {
    const stateToSave = {
      courts: state.courts,
      bookings: state.bookings,
      operatingHours: state.operatingHours,
      holidays: state.holidays
    };
    localStorage.setItem('padelBookingState', JSON.stringify(stateToSave));
  }, [state.courts, state.bookings, state.operatingHours, state.holidays]);

  const value = {
    state,
    actions
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the context
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export default AppContext;
