import { format, isAfter, isBefore, isToday, parseISO } from 'date-fns';

// Filter courts based on criteria
export const filterCourts = (courts, filters) => {
  let filtered = [...courts];

  // Filter by availability
  if (filters.availability !== 'all') {
    filtered = filtered.filter(court => court.availability === filters.availability);
  }

  // Filter by rating
  if (filters.rating !== 'all') {
    const minRating = parseFloat(filters.rating);
    filtered = filtered.filter(court => court.rating >= minRating);
  }

  // Filter by amenities
  if (filters.amenities && filters.amenities.length > 0) {
    filtered = filtered.filter(court =>
      filters.amenities.every(amenity => court.amenities.includes(amenity))
    );
  }

  // Filter by price range
  if (filters.priceMin !== undefined && filters.priceMax !== undefined) {
    filtered = filtered.filter(court =>
      court.pricePerHour >= filters.priceMin && court.pricePerHour <= filters.priceMax
    );
  }

  return filtered;
};

// Filter bookings based on criteria
export const filterBookings = (bookings, filters) => {
  let filtered = [...bookings];

  // Filter by status
  if (filters.status && filters.status !== 'all') {
    filtered = filtered.filter(booking => booking.status === filters.status);
  }

  // Filter by court
  if (filters.court && filters.court !== 'all') {
    const courtId = parseInt(filters.court);
    filtered = filtered.filter(booking => booking.court.id === courtId);
  }

  // Filter by date range
  if (filters.dateRange) {
    const { start, end } = filters.dateRange;
    filtered = filtered.filter(booking => {
      const bookingDate = new Date(booking.date);
      return bookingDate >= start && bookingDate <= end;
    });
  }

  // Filter by search term
  if (filters.searchTerm) {
    const term = filters.searchTerm.toLowerCase();
    filtered = filtered.filter(booking =>
      booking.customer.firstName.toLowerCase().includes(term) ||
      booking.customer.lastName.toLowerCase().includes(term) ||
      booking.customer.email.toLowerCase().includes(term) ||
      booking.id.toLowerCase().includes(term) ||
      booking.court.name.toLowerCase().includes(term)
    );
  }

  return filtered;
};

// Get booking status based on date and current status
export const getBookingStatus = (booking) => {
  const bookingDate = new Date(booking.date);
  const now = new Date();

  if (booking.status === 'cancelled') return 'cancelled';
  if (isBefore(bookingDate, now) && !isToday(bookingDate)) return 'completed';
  if (isToday(bookingDate)) return 'today';
  return 'upcoming';
};

// Sort bookings by date and time
export const sortBookings = (bookings, sortBy = 'date', order = 'asc') => {
  return [...bookings].sort((a, b) => {
    let aValue, bValue;

    switch (sortBy) {
      case 'date':
        aValue = new Date(`${a.date} ${a.timeSlot.startTime}`);
        bValue = new Date(`${b.date} ${b.timeSlot.startTime}`);
        break;
      case 'court':
        aValue = a.court.name;
        bValue = b.court.name;
        break;
      case 'customer':
        aValue = `${a.customer.firstName} ${a.customer.lastName}`;
        bValue = `${b.customer.firstName} ${b.customer.lastName}`;
        break;
      case 'price':
        aValue = a.totalPrice;
        bValue = b.totalPrice;
        break;
      case 'status':
        aValue = a.status;
        bValue = b.status;
        break;
      default:
        aValue = a.createdAt;
        bValue = b.createdAt;
    }

    if (order === 'desc') {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
    }
    return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
  });
};

// Calculate statistics from bookings
export const calculateBookingStats = (bookings) => {
  const totalBookings = bookings.length;
  const totalRevenue = bookings
    .filter(booking => booking.paymentStatus === 'paid')
    .reduce((sum, booking) => sum + booking.totalPrice, 0);

  const activeBookings = bookings.filter(booking => {
    const status = getBookingStatus(booking);
    return status === 'today' || status === 'upcoming';
  }).length;

  const completedBookings = bookings.filter(booking => 
    getBookingStatus(booking) === 'completed'
  ).length;

  const cancelledBookings = bookings.filter(booking => 
    booking.status === 'cancelled'
  ).length;

  const pendingBookings = bookings.filter(booking => 
    booking.status === 'pending'
  ).length;

  return {
    totalBookings,
    totalRevenue,
    activeBookings,
    completedBookings,
    cancelledBookings,
    pendingBookings,
    completionRate: totalBookings > 0 ? (completedBookings / totalBookings) * 100 : 0,
    cancellationRate: totalBookings > 0 ? (cancelledBookings / totalBookings) * 100 : 0
  };
};

// Calculate court utilization
export const calculateCourtUtilization = (courts, bookings, dateRange) => {
  const utilization = courts.map(court => {
    const courtBookings = bookings.filter(booking => 
      booking.court.id === court.id &&
      booking.status !== 'cancelled'
    );

    if (dateRange) {
      const filteredBookings = courtBookings.filter(booking => {
        const bookingDate = new Date(booking.date);
        return bookingDate >= dateRange.start && bookingDate <= dateRange.end;
      });
      
      // Calculate utilization percentage based on available hours
      const totalHours = 14; // Assuming 8 AM to 10 PM = 14 hours
      const bookedHours = filteredBookings.length;
      const utilizationRate = (bookedHours / (totalHours * 7)) * 100; // Weekly calculation
      
      return {
        courtId: court.id,
        courtName: court.name,
        bookedHours,
        totalHours: totalHours * 7,
        utilizationRate: Math.min(utilizationRate, 100)
      };
    }

    return {
      courtId: court.id,
      courtName: court.name,
      totalBookings: courtBookings.length,
      revenue: courtBookings
        .filter(booking => booking.paymentStatus === 'paid')
        .reduce((sum, booking) => sum + booking.totalPrice, 0)
    };
  });

  return utilization;
};

// Generate time slots for a specific court and date
export const generateTimeSlots = (court, date, operatingHours, existingBookings = [], holidays = []) => {
  // Check if date is a holiday
  const holiday = holidays.find(h => h.date === format(new Date(date), 'yyyy-MM-dd'));
  if (holiday && holiday.isFullDay) {
    return [];
  }

  const dayOfWeek = format(new Date(date), 'EEEE').toLowerCase();
  let dayHours = operatingHours[dayOfWeek];

  // Use holiday hours if it's a partial holiday
  if (holiday && !holiday.isFullDay) {
    dayHours = holiday.hours;
  }

  if (!dayHours || !dayHours.isOpen) {
    return [];
  }

  const slots = [];
  const startHour = parseInt(dayHours.open.split(':')[0]);
  const endHour = parseInt(dayHours.close.split(':')[0]);

  for (let hour = startHour; hour < endHour; hour++) {
    const startTime = `${hour.toString().padStart(2, '0')}:00`;
    const endTime = `${(hour + 1).toString().padStart(2, '0')}:00`;

    // Check if slot is booked
    const isBooked = existingBookings.some(booking =>
      booking.court.id === court.id &&
      booking.date === format(new Date(date), 'yyyy-MM-dd') &&
      booking.timeSlot.startTime === startTime &&
      (booking.status === 'confirmed' || booking.status === 'pending')
    );

    const bookedBooking = existingBookings.find(booking =>
      booking.court.id === court.id &&
      booking.date === format(new Date(date), 'yyyy-MM-dd') &&
      booking.timeSlot.startTime === startTime &&
      (booking.status === 'confirmed' || booking.status === 'pending')
    );

    slots.push({
      id: `${court.id}-${format(new Date(date), 'yyyy-MM-dd')}-${hour}`,
      startTime,
      endTime,
      price: court.pricePerHour,
      maxPlayers: court.capacity,
      isBooked,
      isUnavailable: false, // Can be set based on maintenance or other factors
      bookedBy: bookedBooking ? `${bookedBooking.customer.firstName} ${bookedBooking.customer.lastName.charAt(0)}.` : null
    });
  }

  return slots;
};

// Validate booking data
export const validateBooking = (bookingData) => {
  const errors = {};

  if (!bookingData.firstName?.trim()) {
    errors.firstName = 'First name is required';
  }

  if (!bookingData.lastName?.trim()) {
    errors.lastName = 'Last name is required';
  }

  if (!bookingData.email?.trim()) {
    errors.email = 'Email is required';
  } else if (!/\S+@\S+\.\S+/.test(bookingData.email)) {
    errors.email = 'Email is invalid';
  }

  if (!bookingData.phone?.trim()) {
    errors.phone = 'Phone number is required';
  }

  if (!bookingData.players || bookingData.players < 1) {
    errors.players = 'At least 1 player is required';
  }

  if (bookingData.players > bookingData.timeSlot?.maxPlayers) {
    errors.players = `Maximum ${bookingData.timeSlot.maxPlayers} players allowed`;
  }

  if (!bookingData.court) {
    errors.court = 'Court selection is required';
  }

  if (!bookingData.date) {
    errors.date = 'Date selection is required';
  }

  if (!bookingData.timeSlot) {
    errors.timeSlot = 'Time slot selection is required';
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

// Format currency
export const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
};

// Format date for display
export const formatDisplayDate = (date) => {
  return format(new Date(date), 'EEEE, MMMM d, yyyy');
};

// Format time for display
export const formatDisplayTime = (time) => {
  return format(new Date(`2000-01-01 ${time}`), 'h:mm a');
};
