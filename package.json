{"name": "booking-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "cap:init": "cap init", "cap:add:android": "cap add android", "cap:add:ios": "cap add ios", "cap:sync": "npm run build && cap sync", "cap:open:android": "cap open android", "cap:open:ios": "cap open ios", "cap:run:android": "npm run build && cap sync && cap run android", "cap:run:ios": "npm run build && cap sync && cap run ios", "mobile:build": "npm run build && cap sync"}, "dependencies": {"clsx": "^2.1.1", "date-fns": "^3.6.0", "lucide-react": "^0.400.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^6.26.0"}, "devDependencies": {"@capacitor/android": "^6.0.0", "@capacitor/cli": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/ios": "^6.0.0", "@eslint/js": "^9.30.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "^5.8.3", "vite": "^7.0.4"}}