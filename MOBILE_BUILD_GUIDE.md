# Mobile App Build Guide

This guide will help you build Android APK and iOS IPA files from your React web app using Capacitor.

## Prerequisites

### For Android:
- **Android Studio** (latest version)
- **Java Development Kit (JDK) 17+**
- **Android SDK** (API level 33+)

### For iOS (macOS only):
- **Xcode** (latest version)
- **iOS SDK**
- **Apple Developer Account** (for distribution)

## Setup Instructions

### 1. Install Dependencies

```bash
# Install Capacitor dependencies
npm install

# Install Capacitor CLI globally (optional but recommended)
npm install -g @capacitor/cli
```

### 2. Initialize Capacitor (if not already done)

```bash
# Initialize Capacitor
npx cap init

# Add platforms
npx cap add android
npx cap add ios
```

### 3. Build the Web App

```bash
# Build the React app for production
npm run build
```

### 4. Sync with Mobile Platforms

```bash
# Sync the built web app with mobile platforms
npx cap sync

# Or use the npm script
npm run mobile:build
```

## Building Android APK

### Method 1: Using Android Studio (Recommended)

1. **Open Android Studio:**
   ```bash
   npx cap open android
   ```

2. **In Android Studio:**
   - Wait for Gradle sync to complete
   - Go to `Build` → `Build Bundle(s) / APK(s)` → `Build APK(s)`
   - The APK will be generated in `android/app/build/outputs/apk/debug/`

3. **For Release APK:**
   - Go to `Build` → `Generate Signed Bundle / APK`
   - Choose APK and follow the signing process
   - Create a keystore if you don't have one

### Method 2: Command Line

```bash
# Navigate to android directory
cd android

# Build debug APK
./gradlew assembleDebug

# Build release APK (requires signing)
./gradlew assembleRelease
```

## Building iOS IPA (macOS only)

### Method 1: Using Xcode (Recommended)

1. **Open Xcode:**
   ```bash
   npx cap open ios
   ```

2. **In Xcode:**
   - Select your development team
   - Choose a device or simulator
   - Go to `Product` → `Archive`
   - Use the Organizer to export the IPA

### Method 2: Command Line

```bash
# Build for device
npx cap run ios --device

# Build for simulator
npx cap run ios
```

## App Store Distribution

### Android (Google Play Store)

1. **Create a signed APK or AAB:**
   - Use Android Studio's signing wizard
   - Or use command line with your keystore

2. **Upload to Google Play Console:**
   - Create a new app listing
   - Upload your signed APK/AAB
   - Fill in app details and screenshots

### iOS (Apple App Store)

1. **Archive in Xcode:**
   - Product → Archive
   - Use Organizer to validate and upload

2. **App Store Connect:**
   - Create new app listing
   - Upload build through Xcode or Application Loader
   - Submit for review

## Testing

### Android Testing

```bash
# Run on connected Android device
npm run cap:run:android

# Or manually install APK
adb install path/to/your/app.apk
```

### iOS Testing

```bash
# Run on connected iOS device
npm run cap:run:ios

# Or use Xcode to install on device/simulator
```

## Troubleshooting

### Common Android Issues

1. **Gradle Build Failed:**
   - Check Java version (should be JDK 17+)
   - Update Android SDK and build tools
   - Clean and rebuild: `./gradlew clean`

2. **App Crashes on Launch:**
   - Check `android/app/src/main/AndroidManifest.xml`
   - Verify permissions are correctly set
   - Check logs: `adb logcat`

### Common iOS Issues

1. **Code Signing Issues:**
   - Ensure you have a valid Apple Developer account
   - Check provisioning profiles in Xcode
   - Verify bundle identifier is unique

2. **Build Errors:**
   - Update Xcode to latest version
   - Clean build folder: Product → Clean Build Folder
   - Check iOS deployment target compatibility

## App Configuration

### Customizing App Details

Edit `capacitor.config.ts`:

```typescript
const config: CapacitorConfig = {
  appId: 'com.yourcompany.padelbook',  // Change this
  appName: 'PadelBook',
  webDir: 'dist',
  // ... other config
};
```

### App Icons

- Replace icons in `public/icons/` directory
- Use different sizes: 72x72, 96x96, 128x128, 144x144, 152x152, 192x192, 384x384, 512x512
- For iOS: Use Xcode's App Icon set in `ios/App/App/Assets.xcassets/AppIcon.appiconset/`
- For Android: Use Android Studio's Image Asset Studio

### Splash Screen

- Configure in `capacitor.config.ts`
- Add splash screen images to respective platform folders

## Performance Tips

1. **Optimize Bundle Size:**
   ```bash
   npm run build -- --analyze
   ```

2. **Enable Compression:**
   - Gzip compression for web assets
   - Optimize images and fonts

3. **Lazy Loading:**
   - Implement code splitting
   - Lazy load routes and components

## Useful Commands

```bash
# Development
npm run dev                    # Start web dev server
npm run cap:sync              # Sync web app with mobile platforms

# Building
npm run build                 # Build web app
npm run mobile:build          # Build and sync with mobile

# Mobile Development
npm run cap:open:android      # Open Android Studio
npm run cap:open:ios          # Open Xcode
npm run cap:run:android       # Run on Android device
npm run cap:run:ios           # Run on iOS device

# Maintenance
npx cap doctor                # Check Capacitor setup
npx cap update                # Update Capacitor dependencies
```

## Next Steps

1. **Test thoroughly** on real devices
2. **Add native features** like push notifications, camera access
3. **Optimize performance** for mobile networks
4. **Submit to app stores** following their guidelines
5. **Set up CI/CD** for automated builds

For more detailed information, visit the [Capacitor Documentation](https://capacitorjs.com/docs).
